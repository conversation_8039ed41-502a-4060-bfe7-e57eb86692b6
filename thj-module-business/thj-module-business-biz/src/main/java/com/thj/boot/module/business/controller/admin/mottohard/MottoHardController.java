package com.thj.boot.module.business.controller.admin.mottohard;


import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.convert.mottohard.MottoHardConvert;
import com.thj.boot.module.business.dal.datado.hisdrug.HisDrugDO;
import com.thj.boot.module.business.dal.datado.mottohard.MottoHardDO;
import com.thj.boot.module.business.pojo.drug.vo.DrugRespVO;
import com.thj.boot.module.business.pojo.drugtype.vo.DrugTypeRespVO;
import com.thj.boot.module.business.pojo.mottohard.vo.MottoHardCreateReqVO;
import com.thj.boot.module.business.pojo.mottohard.vo.MottoHardPageReqVO;
import com.thj.boot.module.business.pojo.mottohard.vo.MottoHardRespVO;
import com.thj.boot.module.business.pojo.mottohard.vo.MottoHardUpdateReqVO;
import com.thj.boot.module.business.service.mottohard.MottoHardService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 透析方案
 */
@RestController
@RequestMapping("/business/motto-hard")
@Validated
public class MottoHardController {

    @Resource
    private MottoHardService mottoHardService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createMottoHard(@RequestBody MottoHardCreateReqVO createReqVO) {
        return success(mottoHardService.createMottoHard(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateMottoHard(@RequestBody MottoHardUpdateReqVO updateReqVO) {
        mottoHardService.updateMottoHard(updateReqVO);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteMottoHard(@RequestParam("id") Long id) {
        mottoHardService.deleteMottoHard(id);
        return success(true);
    }

    /**
     * 详情
     */
    @GetMapping("/get")
    public CommonResult<MottoHardRespVO> getMottoHard(String dialyzeId) {
        return success(mottoHardService.getMottoHard(dialyzeId));
    }


    /**
     * 列表
     */
    @GetMapping("/list")
    public CommonResult<List<MottoHardRespVO>> getMottoHardList(MottoHardCreateReqVO createReqVO) {
        List<MottoHardDO> list = mottoHardService.getMottoHardList(createReqVO);
        return success(MottoHardConvert.INSTANCE.convertList(list));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<MottoHardRespVO>> getMottoHardPage(@RequestBody MottoHardPageReqVO pageVO) {
        PageResult<MottoHardDO> pageResult = mottoHardService.getMottoHardPage(pageVO);
        return success(MottoHardConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 根据药品多个id获取对应的药品信息（只针对抗凝剂）
     */
    @GetMapping("/getDrugList")
    public CommonResult<List<DrugRespVO>> getDrugList(String drugTypeIds) {
        return success(mottoHardService.getDrugList(drugTypeIds));
    }

    /**
     * 获取西药下的抗凝剂id
     */
    @GetMapping("/getDrugTypeList")
    public CommonResult<List<DrugTypeRespVO>> getDrugTypeList(Long id) {
        return success(mottoHardService.getDrugTypeList(id));
    }

    /**
     * 查询药品字典his和手动新增
     */
    @GetMapping("/getDrugDictionary")
    public CommonResult<List<HisDrugDO>> getDrugDictionary(String name) {
        return success(mottoHardService.getDrugDictionary(name));
    }

}
