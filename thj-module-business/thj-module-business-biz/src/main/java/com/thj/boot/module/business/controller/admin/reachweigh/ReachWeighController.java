package com.thj.boot.module.business.controller.admin.reachweigh;

import com.thj.boot.common.annotation.OperateLog;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.common.pojo.PageResult;
import com.thj.boot.module.business.controller.admin.reachweigh.vo.ReachWeighWeekRespVO;
import com.thj.boot.module.business.pojo.arrangeclasses.vo.ArrangeClassesRespVO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighCreateReqVO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighPageReqVO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighRespVO;
import com.thj.boot.module.business.pojo.reachweigh.vo.ReachWeighUpdateReqVO;
import com.thj.boot.module.business.service.reachweigh.ReachWeighService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static com.thj.boot.common.pojo.CommonResult.success;

/**
 * 签到体重
 */
@RestController
@RequestMapping("/business/reach-weigh")
public class ReachWeighController {

    @Resource
    private ReachWeighService reachWeighService;

    /**
     * 新增
     */
    @PostMapping("/create")
    public CommonResult<Long> createReachWeigh(@RequestBody ReachWeighCreateReqVO createReqVO) {
        return success(reachWeighService.createReachWeigh(createReqVO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public CommonResult<Boolean> updateReachWeigh(@RequestBody ReachWeighUpdateReqVO updateReqVO) {
        reachWeighService.updateReachWeigh(updateReqVO);
        return success(true);
    }

    /**
     * 新增或修改
     */
    @PostMapping("/saveOrUpdate")
    @OperateLog("新增或修签到称重")
    public CommonResult<Boolean> saveOrUpdateReachWeigh(@RequestBody ReachWeighUpdateReqVO updateReqVO, HttpServletRequest request) {
        reachWeighService.saveOrUpdateReachWeigh(updateReqVO, request);
        return success(true);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    public CommonResult<Boolean> deleteReachWeigh(@RequestParam("id") Long id) {
        reachWeighService.deleteReachWeigh(id);
        return success(true);
    }

    /**
     * 详情
     */
    @PostMapping("/get")
    public CommonResult<ReachWeighRespVO> getReachWeigh(@RequestBody ReachWeighCreateReqVO reachWeighCreateReqVO) {
        return success(reachWeighService.getReachWeigh(reachWeighCreateReqVO));
    }

    /**
     * 不分页
     */
    @GetMapping("/list")
    public CommonResult<List<ReachWeighRespVO>> getReachWeighList(ReachWeighCreateReqVO createReqVO) {
        return success(reachWeighService.getReachWeighList(createReqVO));
    }

    /**
     * 分页
     */
    @PostMapping("/page")
    public CommonResult<PageResult<ReachWeighRespVO>> getReachWeighPage(@RequestBody ReachWeighPageReqVO pageVO) {
        return success(reachWeighService.getReachWeighPage(pageVO));
    }

    /**
     * 左侧患者列表
     */
    @GetMapping("/teamPatientList")
    public CommonResult<List<ArrangeClassesRespVO>> teamPatientList(ReachWeighCreateReqVO createReqVO) {
        return success(reachWeighService.teamPatientList(createReqVO));
    }


    /**
     * 左侧统计班次
     */
    @GetMapping("/weekFlagList")
    public CommonResult<List<ReachWeighWeekRespVO>> weekFlagList() {
        return success(reachWeighService.weekFlagList());
    }


    /**
     * 对接人脸称重签到
     */
    @PostMapping("/getFaceWeight")
    public CommonResult<Boolean> getFaceWeight(@RequestBody ReachWeighUpdateReqVO updateReqVO, HttpServletRequest request) {
        reachWeighService.getFaceWeight(updateReqVO, request);
        return success(true);
    }

}
