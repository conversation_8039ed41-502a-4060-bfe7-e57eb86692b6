package com.thj.boot.module.business.service.aiScore;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.thj.boot.common.exception.ServiceException;
import com.thj.boot.common.utils.StringUtils;
import com.thj.boot.module.business.config.AiConfig;
import com.thj.boot.module.business.convert.aiScore.AiScoreConvert;
import com.thj.boot.module.business.dal.datado.aiScore.AiScoreDO;
import com.thj.boot.module.business.dal.mapper.aiScore.AiScoreMapper;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreCreateReqVO;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreFormattedRespVO;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreRenalCheckVO;
import com.thj.boot.module.business.pojo.aiscore.vo.AiScoreRespVO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordCreateReqVO;
import com.thj.boot.module.business.pojo.firstcourserecord.vo.FirstCourseRecordRespVO;
import com.thj.boot.module.business.pojo.outpatientblood.vo.OutpatientBloodRespVO;
import com.thj.boot.module.business.pojo.renalproject.vo.RenalProjectExportReqVO;
import com.thj.boot.module.business.pojo.renalproject.vo.RenalProjectRespVO;
import com.thj.boot.module.business.pojo.renalproject.vo.RenalProjectUpdateReqVO;
import com.thj.boot.module.business.service.ai.AiService;
import com.thj.boot.module.business.service.firstcourserecord.FirstCourseRecordService;
import com.thj.boot.module.business.service.outpatientblood.OutpatientBloodService;
import com.thj.boot.module.business.service.renalproject.RenalProjectService;
import com.thj.boot.module.business.util.AiScoreFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 病历首页 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class AiScoreServiceImpl implements AiScoreService {
    
    // ==================== 常量定义 ====================
    
    /**
     * 肾科检验相关字典ID常量
     */
    private static final class RenalDictConstants {
        /** 血常规 */
        static final Long BLOOD_ROUTINE = 728L;
        /** 电解质 */
        static final Long ELECTROLYTE = 735L;
        /** 甲状腺激素 */
        static final Long THYROID_HORMONE = 736L;
    }
    
    /**
     * 医疗数据转换器 - 统一管理所有数据字典和转换规则
     */
    private static final class MedicalDataTransformer {
        
        // 预编译的正则表达式模式（性能优化）
        private static final Pattern TREATMENT_HISTORY_PATTERN = Pattern.compile("\"treatmentHistory\":\\[(\\d+(?:,\\d+)*)\\]");
        private static final Pattern FAMILY_HISTORY_PATTERN = Pattern.compile("\"familyHistory\":\\[\"(\\d+)\"(?:,\"(\\d+)\")*\\]");
        private static final Pattern NUM_PATTERN = Pattern.compile("\"(\\d+)\"");
        
        // 治疗史映射
        private static final Map<String, String> TREATMENT_HISTORY_MAP = new LinkedHashMap<>();
        static {
            TREATMENT_HISTORY_MAP.put("1", "腹透史");
            TREATMENT_HISTORY_MAP.put("2", "肾移植");
            TREATMENT_HISTORY_MAP.put("3", "内瘘");
            TREATMENT_HISTORY_MAP.put("4", "自体血管");
            TREATMENT_HISTORY_MAP.put("5", "人工血管");
            TREATMENT_HISTORY_MAP.put("6", "长期置管");
            TREATMENT_HISTORY_MAP.put("7", "临时置管");
        }
        
        // 家族史映射
        private static final Map<String, String> FAMILY_HISTORY_MAP = new LinkedHashMap<>();
        static {
            FAMILY_HISTORY_MAP.put("1", "高血压家族史");
            FAMILY_HISTORY_MAP.put("2", "糖尿病家族史");
            FAMILY_HISTORY_MAP.put("3", "心脏病家族史");
            FAMILY_HISTORY_MAP.put("4", "肾病家族史");
            FAMILY_HISTORY_MAP.put("5", "肿瘤家族史");
        }
        
        // 简单字段替换映射（减少replaceAll操作）
        private static final Map<String, String> SIMPLE_FIELD_MAP = new LinkedHashMap<>();
        static {
            // 慢性病史
            SIMPLE_FIELD_MAP.put("\"chronicDiseasesHistory\":1", "\"chronicDiseasesHistory\":\"有\"");
            SIMPLE_FIELD_MAP.put("\"chronicDiseasesHistory\":2", "\"chronicDiseasesHistory\":\"无\"");
            // 疫苗接种史
            SIMPLE_FIELD_MAP.put("\"vaccination\":1", "\"vaccination\":\"已接种\"");
            SIMPLE_FIELD_MAP.put("\"vaccination\":2", "\"vaccination\":\"未接种\"");
            // 食物过敏史
            SIMPLE_FIELD_MAP.put("\"foodAllergies\":1", "\"foodAllergies\":\"有\"");
            SIMPLE_FIELD_MAP.put("\"foodAllergies\":2", "\"foodAllergies\":\"无\"");
            // 外伤手术史
            SIMPLE_FIELD_MAP.put("\"traumaSurgery\":1", "\"traumaSurgery\":\"有\"");
            SIMPLE_FIELD_MAP.put("\"traumaSurgery\":2", "\"traumaSurgery\":\"无\"");
            // 居住地
            SIMPLE_FIELD_MAP.put("\"placeResidence\":1", "\"placeResidence\":\"城市\"");
            SIMPLE_FIELD_MAP.put("\"placeResidence\":2", "\"placeResidence\":\"农村\"");
            // 不良习惯
            SIMPLE_FIELD_MAP.put("\"badHabit\":1", "\"badHabit\":\"有\"");
            SIMPLE_FIELD_MAP.put("\"badHabit\":2", "\"badHabit\":\"无\"");
            // 接触史
            SIMPLE_FIELD_MAP.put("\"contact\":1", "\"contact\":\"有\"");
            SIMPLE_FIELD_MAP.put("\"contact\":2", "\"contact\":\"无\"");
            // 体格检查字段
            SIMPLE_FIELD_MAP.put("\"shape\":\"1\"", "\"shape\":\"正常\"");
            SIMPLE_FIELD_MAP.put("\"shape\":\"2\"", "\"shape\":\"肥胖\"");
            SIMPLE_FIELD_MAP.put("\"shape\":\"3\"", "\"shape\":\"消瘦\"");
            SIMPLE_FIELD_MAP.put("\"develop\":\"1\"", "\"develop\":\"正常\"");
            SIMPLE_FIELD_MAP.put("\"develop\":\"2\"", "\"develop\":\"不良\"");
            SIMPLE_FIELD_MAP.put("\"mind\":\"1\"", "\"mind\":\"清楚\"");
            SIMPLE_FIELD_MAP.put("\"mind\":\"2\"", "\"mind\":\"不清\"");
            SIMPLE_FIELD_MAP.put("\"position\":\"1\"", "\"position\":\"自主\"");
            SIMPLE_FIELD_MAP.put("\"position\":\"2\"", "\"position\":\"被动\"");
            // 化验字段重命名
            SIMPLE_FIELD_MAP.put("\"ecg\"", "\"胸部X线检查\"");
            SIMPLE_FIELD_MAP.put("\"chestXray\"", "\"胸部X线检查\"");
            SIMPLE_FIELD_MAP.put("\"otherLab\"", "\"其他化验检查\"");
        }
        
        // 症状关键词数组（用于主诉优化）
        private static final String[] SYMPTOM_KEYWORDS = {
            "头晕", "乏力", "下肢浮肿", "恶心", "呕吐", "食欲减退",
            "胸闷", "心悸", "气促", "头痛", "水肿", "尿少", "无尿"
        };
        
        /**
         * 统一数据转换入口 - 替换原来分散的转换方法
         */
        public static String transformMedicalData(String content) {
            if (StringUtils.isBlank(content)) {
                return content;
            }
            
            try {
                // 1. 清理null值
                content = content.replace("\"chiefComplaint\":null,", "");
                
                // 2. 批量简单字段替换（性能优化：一次遍历完成所有替换）
                content = batchReplaceSimpleFields(content);
                
                // 3. 复杂数组字段转换
                content = transformArrayFields(content);
                
                // 4. 医疗记录格式优化
                content = optimizeFormat(content);
                
                return content;
                
            } catch (Exception e) {
                log.error("医疗数据转换失败", e);
                return content; // 降级：返回原始内容
            }
        }
        
        /**
         * 批量替换简单字段（性能优化）
         */
        private static String batchReplaceSimpleFields(String content) {
            StringBuilder sb = new StringBuilder(content);
            
            for (Map.Entry<String, String> entry : SIMPLE_FIELD_MAP.entrySet()) {
                String oldStr = entry.getKey();
                String newStr = entry.getValue();
                
                int index = 0;
                while ((index = sb.indexOf(oldStr, index)) != -1) {
                    sb.replace(index, index + oldStr.length(), newStr);
                    index += newStr.length();
                }
            }
            
            return sb.toString();
        }
        
        /**
         * 转换数组字段
         */
        private static String transformArrayFields(String content) {
            // 转换治疗史
            content = transformTreatmentHistory(content);
            // 转换家族史  
            content = transformFamilyHistory(content);
            return content;
        }
        
        /**
         * 转换治疗史（优化版）
         */
        private static String transformTreatmentHistory(String content) {
            Matcher matcher = TREATMENT_HISTORY_PATTERN.matcher(content);
            StringBuffer sb = new StringBuffer();
            
            while (matcher.find()) {
                String[] values = matcher.group(1).split(",");
                StringBuilder result = new StringBuilder("\"treatmentHistory\":\"");
                
                for (int i = 0; i < values.length; i++) {
                    if (i > 0) result.append(",");
                    String value = values[i].trim();
                    result.append(TREATMENT_HISTORY_MAP.getOrDefault(value, "未知(" + value + ")"));
                }
                result.append("\"");
                matcher.appendReplacement(sb, result.toString());
            }
            matcher.appendTail(sb);
            
            return sb.toString();
        }
        
        /**
         * 转换家族史（优化版）
         */
        private static String transformFamilyHistory(String content) {
            Matcher familyMatcher = FAMILY_HISTORY_PATTERN.matcher(content);
            StringBuffer familySb = new StringBuffer();
            
            while (familyMatcher.find()) {
                String fullMatch = familyMatcher.group(0);
                Matcher numMatcher = NUM_PATTERN.matcher(fullMatch);
                StringBuilder result = new StringBuilder("\"familyHistory\":\"");
                boolean first = true;
                
                while (numMatcher.find()) {
                    if (!first) result.append(",");
                    first = false;
                    String value = numMatcher.group(1);
                    result.append(FAMILY_HISTORY_MAP.getOrDefault(value, "其他家族史(" + value + ")"));
                }
                result.append("\"");
                familyMatcher.appendReplacement(familySb, result.toString());
            }
            familyMatcher.appendTail(familySb);
            
            return familySb.toString();
        }
        
        /**
         * 优化医疗记录格式
         */
        private static String optimizeFormat(String content) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                JsonNode root = mapper.readTree(content);
                
                // 优化主诉
                content = optimizeChiefComplaint(content, root);
                
                return content;
            } catch (Exception e) {
                log.warn("格式优化失败: {}", e.getMessage());
                return content;
            }
        }
        
        /**
         * 优化主诉格式
         */
        private static String optimizeChiefComplaint(String content, JsonNode root) {
            try {
                JsonNode mainSuitNode = root.get("mainSuit");
                JsonNode presentMedicalNode = root.get("presentMedical");
                
                if (mainSuitNode != null && presentMedicalNode != null) {
                    String mainSuit = mainSuitNode.asText();
                    String presentMedical = presentMedicalNode.asText();
                    
                    if (mainSuit.contains("维持性血液透析") && 
                        !mainSuit.contains("乏力") && !mainSuit.contains("水肿")) {
                        
                        String symptoms = extractSymptoms(presentMedical);
                        if (StringUtils.isNotBlank(symptoms)) {
                            String enhancedMainSuit = symptoms + "，" + mainSuit;
                            content = content.replace("\"mainSuit\":\"" + mainSuit + "\"", 
                                                    "\"mainSuit\":\"" + enhancedMainSuit + "\"");
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("主诉优化失败: {}", e.getMessage());
            }
            
            return content;
        }
        
        /**
         * 提取症状关键词
         */
        private static String extractSymptoms(String text) {
            if (StringUtils.isBlank(text)) {
                return "";
            }
            
            StringBuilder symptoms = new StringBuilder();
            int count = 0;
            
            for (String keyword : SYMPTOM_KEYWORDS) {
                if (text.contains(keyword) && count < 3) {
                    if (symptoms.length() > 0) {
                        symptoms.append("、");
                    }
                    symptoms.append(keyword);
                    count++;
                }
            }
            
            return symptoms.toString();
        }
    }

    @Resource
    private AiScoreMapper aiScoreMapper;

    @Resource
    private AiService aiService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private AiConfig aiConfig;

    @Resource
    private OutpatientBloodService outpatientBloodService;

    @Resource
    private RenalProjectService renalProjectService;



    @Resource
    private FirstCourseRecordService firstCourseRecordService;



    @Override
    public AiScoreRespVO getAiScore(Long patientId) {
        AiScoreDO medicalPage2DO = aiScoreMapper.selectOne(AiScoreDO::getPatientId, patientId);
        AiScoreRespVO aiScoreRespVO = new AiScoreRespVO();
        if (StringUtils.isNotNull(medicalPage2DO)) {
            aiScoreRespVO = AiScoreConvert.INSTANCE.convert(medicalPage2DO);
        }

        return aiScoreRespVO;
    }

    @Override
    public String getRenalCheck(Long patientId) {

        // 创建查询参数
        RenalProjectExportReqVO exportReqVO = new RenalProjectExportReqVO();
        exportReqVO.setPatientId(patientId);
        exportReqVO.setProjectType("0"); // 肾科检验
        
        // 获取所有项目
        List<RenalProjectRespVO> renals = renalProjectService.getRenalProjectListAll(exportReqVO, null);
        
        // 查找对应dictId的id
        Long bloodRoutineId = null;
        Long electrolyteId = null;
        Long thyroidHormoneId = null;
        if (renals != null) {
            for (RenalProjectRespVO renal : renals) {
                if (renal.getDictId() != null) {
                    if (renal.getDictId().equals(RenalDictConstants.BLOOD_ROUTINE)) {
                        bloodRoutineId = renal.getId();
                    } else if (renal.getDictId().equals(RenalDictConstants.ELECTROLYTE)) {
                        electrolyteId = renal.getId();
                    } else if (renal.getDictId().equals(RenalDictConstants.THYROID_HORMONE)) {
                        thyroidHormoneId = renal.getId();
                    }
                }
            }
        }
        
        // 创建返回对象
        AiScoreRenalCheckVO result = new AiScoreRenalCheckVO();
        result.setPatientId(patientId);
        
        try {
            // 获取血常规数据 (dictId=728)
            if (bloodRoutineId != null) {
                RenalProjectUpdateReqVO bloodRoutineReq = new RenalProjectUpdateReqVO();
                bloodRoutineReq.setId(bloodRoutineId);
                bloodRoutineReq.setDictId(RenalDictConstants.BLOOD_ROUTINE);
                bloodRoutineReq.setProjectType("0");
                
                RenalProjectRespVO bloodRoutineResp = renalProjectService.getRenalProject(bloodRoutineReq);
                if (bloodRoutineResp != null && bloodRoutineResp.getRenalProjectInfoRespVOList() != null 
                    && !bloodRoutineResp.getRenalProjectInfoRespVOList().isEmpty()) {
                    
                    // 安全获取最新的检验记录（第一条）
                    String projectInfo = bloodRoutineResp.getRenalProjectInfoRespVOList().get(0) != null 
                        ? bloodRoutineResp.getRenalProjectInfoRespVOList().get(0).getProjectInfo() 
                        : null;
                    if (StringUtils.isNotBlank(projectInfo)) {
                        JsonNode jsonNode = objectMapper.readTree(projectInfo);
                        String bloodRoutine2 = jsonNode.has("BloodRoutine2") ? jsonNode.get("BloodRoutine2").asText() : "";
                        result.setBloodRoutine2(bloodRoutine2);
                    }
                }
            }
            
            // 获取电解质数据 (dictId=735)
            if (electrolyteId != null) {
                RenalProjectUpdateReqVO electrolyteReq = new RenalProjectUpdateReqVO();
                electrolyteReq.setId(electrolyteId);
                electrolyteReq.setDictId(RenalDictConstants.ELECTROLYTE);
                electrolyteReq.setProjectType("0");
                
                RenalProjectRespVO electrolyteResp = renalProjectService.getRenalProject(electrolyteReq);
                if (electrolyteResp != null && electrolyteResp.getRenalProjectInfoRespVOList() != null 
                    && !electrolyteResp.getRenalProjectInfoRespVOList().isEmpty()) {
                    
                    // 安全获取最新的检验记录（第一条）
                    String projectInfo = electrolyteResp.getRenalProjectInfoRespVOList().get(0) != null 
                        ? electrolyteResp.getRenalProjectInfoRespVOList().get(0).getProjectInfo() 
                        : null;
                    if (StringUtils.isNotBlank(projectInfo)) {
                        JsonNode jsonNode = objectMapper.readTree(projectInfo);
                        String electrolyte1 = jsonNode.has("electrolyte1") ? jsonNode.get("electrolyte1").asText() : "";
                        String electrolyte8 = jsonNode.has("electrolyte8") ? jsonNode.get("electrolyte8").asText() : "";
                        result.setElectrolyte1(electrolyte1);
                        result.setElectrolyte8(electrolyte8);
                    }
                }
            }
            
            // 获取甲状腺激素数据 (dictId=736)
            if (thyroidHormoneId != null) {
                RenalProjectUpdateReqVO thyroidHormoneReq = new RenalProjectUpdateReqVO();
                thyroidHormoneReq.setId(thyroidHormoneId);
                thyroidHormoneReq.setDictId(RenalDictConstants.THYROID_HORMONE);
                thyroidHormoneReq.setProjectType("0");
                
                RenalProjectRespVO thyroidHormoneResp = renalProjectService.getRenalProject(thyroidHormoneReq);
                if (thyroidHormoneResp != null && thyroidHormoneResp.getRenalProjectInfoRespVOList() != null 
                    && !thyroidHormoneResp.getRenalProjectInfoRespVOList().isEmpty()) {
                    
                    // 安全获取最新的检验记录（第一条）
                    String projectInfo = thyroidHormoneResp.getRenalProjectInfoRespVOList().get(0) != null 
                        ? thyroidHormoneResp.getRenalProjectInfoRespVOList().get(0).getProjectInfo() 
                        : null;
                    if (StringUtils.isNotBlank(projectInfo)) {
                        JsonNode jsonNode = objectMapper.readTree(projectInfo);
                        String thyroidHormone3 = jsonNode.has("thyroidHormone3") ? jsonNode.get("thyroidHormone3").asText() : "";
                        result.setThyroidHormone3(thyroidHormone3);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("解析化验结果JSON数据时发生错误：", e);
        }
        
        // 转换为中文键名的JSON字符串
        String chineseJsonString = null;
        try {
            Map<String, Object> chineseKeyMap = new LinkedHashMap<>();
            chineseKeyMap.put("患者ID", result.getPatientId());
            chineseKeyMap.put("血红蛋白(g/L)", result.getBloodRoutine2());
            chineseKeyMap.put("透析前钾(mmol/L)", result.getElectrolyte1());
            chineseKeyMap.put("透析前磷(mmol/L)", result.getElectrolyte8());
            chineseKeyMap.put("全段甲状旁腺激素(pg/ml)", result.getThyroidHormone3());

            chineseJsonString = objectMapper.writeValueAsString(chineseKeyMap);
            log.info("患者ID: {} 的化验结果中文JSON: {}", patientId, chineseJsonString);

        } catch (Exception e) {
            log.error("转换化验结果为中文JSON时发生错误：", e);
        }

        return chineseJsonString;
    }



    @Override
    public AiScoreFormattedRespVO getFormattedAiScore(Long patientId) {
        AiScoreDO aiScoreDO = aiScoreMapper.selectOne(AiScoreDO::getPatientId, patientId);
        if (aiScoreDO == null) {
            log.warn("未找到患者ID为 {} 的AI评分记录", patientId);
            return null;
        }

        return AiScoreFormatUtil.formatAiScoreResponse(aiScoreDO);
    }

    @Override
    public boolean saveExpertComment(Long patientId, String expertComment) {
        // 参数校验
        if (patientId == null) {
            throw new ServiceException(400, "患者ID不能为空");
        }
        if (StringUtils.isBlank(expertComment)) {
            throw new ServiceException(400, "专家点评内容不能为空");
        }
        
        // 查询该患者最新的AI评分记录
        AiScoreDO aiScoreDO = aiScoreMapper.selectOne(AiScoreDO::getPatientId, patientId);
        if (aiScoreDO == null) {
            // 如果没有找到评分记录，返回false
            return false;
        }
        
        // 更新专家点评
        aiScoreDO.setExpertComment(expertComment);
        int updateResult = aiScoreMapper.updateById(aiScoreDO);
        
        return updateResult > 0;
    }

    @Override
    public AiScoreRespVO createAiScore(AiScoreCreateReqVO reqVO) {
        try {
            // 1. 参数校验
        if (reqVO.getPatientId() == null) {
            throw new ServiceException(400, "患者ID不能为空");
        }
        
            // 2. 获取病历和诊疗信息
            String medicalRecord = null;
            String patientName = null;
            List<String> medicalRecordFromOutpatientBlood = getMedicalRecordFromOutpatientBlood(reqVO.getPatientId());
            if (medicalRecordFromOutpatientBlood != null) {
                medicalRecord = medicalRecordFromOutpatientBlood.get(0);
                patientName = medicalRecordFromOutpatientBlood.get(1);
            }
            
        String treatmentInfo = getTreatmentInfoFromFirstCourseRecord(reqVO.getPatientId());
            
            // 3. 处理空数据情况
        if (medicalRecord == null && treatmentInfo == null) {
            AiScoreRespVO resp = new AiScoreRespVO();
            resp.setTotalScore(0);
            resp.setLevel("无");
            resp.setContent("病历和诊疗信息为空，无法评分。");
            String emptyDataJson = "{\"totalScore\":0,\"level\":\"无\",\"comment\":\"病历和诊疗信息为空，无法评分。\"}";
            resp.setScoreResult(emptyDataJson);
            return resp;
        }
        
            log.info("开始为患者ID: {} ({})创建AI评分", reqVO.getPatientId(),patientName);

            // 4. 构建AI提示词并调用AI服务


            // 检查和优化输入文本长度
            String optimizedMedicalRecord = optimizeTextLength(medicalRecord, "病历内容");
            String optimizedTreatmentInfo = optimizeTextLength(treatmentInfo, "诊疗信息");

            log.info("文本优化完成 - 原始病历长度: {}, 优化后: {}, 原始诊疗信息长度: {}, 优化后: {}", medicalRecord.length(),optimizedMedicalRecord.length(),treatmentInfo.length(),optimizedTreatmentInfo.length());

            // 构建提示词
            String realPrompt = aiService.buildPrompt("score", optimizedMedicalRecord, optimizedTreatmentInfo,"");
            List<String> aiResponses = aiService.scoreMedicalRecord(realPrompt);
            String aiResponse = aiResponses.get(0);
            String aiReasoning = aiResponses.get(1);

            if (StringUtils.isBlank(aiResponse)) {
                log.error("AI服务返回空响应，患者ID: {}", reqVO.getPatientId());
                throw new ServiceException(500, "AI服务返回空响应");
            }

            log.info("AI服务响应成功，患者ID: {},患者姓名: {}, 响应长度: {} 字符", reqVO.getPatientId(),reqVO.getPatientName(), aiResponse.length());

            // 5. 解析AI响应JSON
            JsonNode responseNode;
            try {
                responseNode = objectMapper.readTree(aiResponse);
            } catch (Exception e) {
                log.error("解析AI响应JSON失败，患者ID: {}, 错误: {}", reqVO.getPatientId(), e.getMessage());
                // JSON解析异常返回纯文本，创建一个包含原始响应的默认结构
                throw new ServiceException(500, "AI响应格式错误，原始内容：" + aiResponse);
            }

            // 6. 验证JSON结构并提取数据
            if (!responseNode.has("totalScore") || !responseNode.has("level") || !responseNode.has("details")) {
                log.error("AI评分结果缺少必要字段，患者ID: {}", reqVO.getPatientId());
                throw new ServiceException(500, "AI响应格式错误，缺少必要字段，原始内容：" + aiResponse);
            }

            Integer totalScore = responseNode.get("totalScore").asInt();
            String level = responseNode.get("level").asText();

            log.info("AI评分解析成功，患者ID: {}, 总分: {}, 等级: {}", reqVO.getPatientId(), totalScore, level);

            // 7. 构建并保存DO对象
            AiScoreDO aiScoreDO = new AiScoreDO();
            aiScoreDO.setPatientId(reqVO.getPatientId());
            aiScoreDO.setPatient_name(patientName);
            aiScoreDO.setContent(aiResponse);
            aiScoreDO.setScoreResult(aiResponse);
            aiScoreDO.setScoreTotal(totalScore);
            aiScoreDO.setScoreLevel(level);
            aiScoreDO.setAiModel(aiConfig.getModel());
            aiScoreDO.setAiPrompt(realPrompt);
            aiScoreDO.setAiResponseJson(aiResponse);
            aiScoreDO.setPatientBasicInfo(medicalRecord);
            aiScoreDO.setTreatmentInfo(treatmentInfo);
            aiScoreDO.setAiReasoning(aiReasoning);
            

            // 8. 保存到数据库
            try {
                aiScoreMapper.insert(aiScoreDO);
                log.info("AI评分结果保存成功，患者ID: {}({}), 记录ID: {}, 总分: {}, 等级: {}",
                    reqVO.getPatientId(), aiScoreDO.getId(),patientName, aiScoreDO.getScoreTotal(), aiScoreDO.getScoreLevel());
            } catch (Exception e) {
                log.error("保存AI评分结果到数据库失败，患者ID: {}", reqVO.getPatientId(), e);
                throw new ServiceException(500, "数据库保存失败：" + e.getMessage());
            }

            // 9. 返回结果
            return AiScoreConvert.INSTANCE.convert(aiScoreDO);

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("AI评分过程中发生未知异常，患者ID: {}", reqVO.getPatientId(), e);
            throw new ServiceException(500, "AI评分失败：" + e.getMessage());
        }
    }


    @Override
        public AiScoreRespVO testCreateAiScore(AiScoreCreateReqVO reqVO) {
            // 参数校验
            if (reqVO.getPatientId() == null) {
                throw new ServiceException(400, "患者ID不能为空");
            }






            // 调用AI服务进行评分
            String aiResponse = aiService.testAI();


            AiScoreDO aiScoreDO = new AiScoreDO();
            Date date = new Date();

            String dateString = date.toString();
            aiScoreDO.setContent(aiResponse);
            aiScoreDO.setExpertComment(dateString);

            return AiScoreConvert.INSTANCE.convert(aiScoreDO);
    }

    @Override
    public AiScoreRespVO createAiReports(AiScoreCreateReqVO reqVO) {
        // 1. 数据验证和准备
        validateRequest(reqVO);
        AiReportInputData inputData = prepareInputData(reqVO);
        
        log.info("开始为患者ID: {} ({})创建AI系列报告...", reqVO.getPatientId(), inputData.getPatientName());

        // 2. 生成AI报告
        AiReportResults reportResults = generateAiReports(inputData);

        // 3. 保存到数据库并返回结果
        AiScoreDO savedRecord = saveReportResults(reqVO, inputData, reportResults);
        
        log.info("AI系列报告创建完成, 患者ID: {}, 记录ID: {}", reqVO.getPatientId(), savedRecord.getId());
        return AiScoreConvert.INSTANCE.convert(savedRecord);
    }

    
    /**
     * 优化版：从门诊血液透析简历获取病历信息（使用统一数据转换器）
     */
    private List<String> getMedicalRecordFromOutpatientBlood(Long patientId) {
        OutpatientBloodRespVO outpatientBlood = outpatientBloodService.getOutpatientBlood(patientId);
        if (outpatientBlood == null) {
            return null;
        }
        
        try {
            // 构建结构化病历信息
            MedicalRecordBuilder builder = new MedicalRecordBuilder();
            
            // 基础信息
            builder.addPatientInfo("患者名字", outpatientBlood.getPatientName())
                   .addClinicalInfo("主诉", outpatientBlood.getMainSuit())
                   .addClinicalInfo("现病史", outpatientBlood.getPresentDisease())
                   .addClinicalInfo("初步诊断", outpatientBlood.getPreliminary());
            
            // 处理复杂的content字段
            String content = outpatientBlood.getContent();
            if (StringUtils.isNotBlank(content)) {
                // 使用带缓存的统一数据转换器处理（性能优化）
                content = MedicalDataCache.getCachedTransform(content);
                builder.addOtherContent(content);
            }
            
            // 构建最终结果
            return builder.build(outpatientBlood.getPatientName());
            
        } catch (Exception e) {
            log.error("处理患者{}病历数据时发生异常", patientId, e);
            // 降级处理：返回基础信息
            return createFallbackMedicalRecord(outpatientBlood);
        }
    }
    
    /**
     * 医疗记录构建器 - 结构化构建病历信息
     */
    private static class MedicalRecordBuilder {
        private final StringBuilder medicalRecord = new StringBuilder();
        
        public MedicalRecordBuilder addPatientInfo(String label, String value) {
            if (StringUtils.isNotBlank(value)) {
                medicalRecord.append(label).append("：").append(value).append("。");
            }
            return this;
        }
        
        public MedicalRecordBuilder addClinicalInfo(String label, String value) {
            if (StringUtils.isNotBlank(value)) {
                medicalRecord.append(label).append("：").append(value).append("。");
            }
            return this;
        }
        
        public MedicalRecordBuilder addOtherContent(String content) {
            if (StringUtils.isNotBlank(content)) {
                medicalRecord.append("其他内容：").append(content).append("。");
            }
            return this;
        }
        
        public List<String> build(String patientName) {
            if (medicalRecord.length() > 0) {
                List<String> result = new ArrayList<>();
                result.add(medicalRecord.toString());
                result.add(StringUtils.isNotBlank(patientName) ? patientName : "未知");
                return result;
            }
            return null;
        }
    }
    
    /**
     * 降级处理：创建基础病历信息
     */
    private List<String> createFallbackMedicalRecord(OutpatientBloodRespVO outpatientBlood) {
        StringBuilder fallbackRecord = new StringBuilder();
        
        if (StringUtils.isNotBlank(outpatientBlood.getPatientName())) {
            fallbackRecord.append("患者名字：").append(outpatientBlood.getPatientName()).append("。");
        }
        if (StringUtils.isNotBlank(outpatientBlood.getMainSuit())) {
            fallbackRecord.append("主诉：").append(outpatientBlood.getMainSuit()).append("。");
        }
        
        if (fallbackRecord.length() > 0) {
            List<String> result = new ArrayList<>();
            result.add(fallbackRecord.toString());
            result.add(outpatientBlood.getPatientName());
            return result;
        }
        
        return null;
    }
    
    /**
     * 医疗数据缓存管理器 - 提升性能
     */
    private static final class MedicalDataCache {
        private static final int MAX_CACHE_SIZE = 100;
        private static final Map<String, String> transformCache = 
            Collections.synchronizedMap(new LinkedHashMap<String, String>(MAX_CACHE_SIZE, 0.75f, true) {
                @Override
                protected boolean removeEldestEntry(Map.Entry<String, String> eldest) {
                    return size() > MAX_CACHE_SIZE;
                }
            });
        
        /**
         * 带缓存的数据转换
         */
        public static String getCachedTransform(String content) {
            if (StringUtils.isBlank(content)) {
                return content;
            }
            
            // 为长内容生成缓存键（避免内存溢出）
            String cacheKey = content.length() > 1000 ? 
                String.valueOf(content.hashCode()) : content;
            
            return transformCache.computeIfAbsent(cacheKey, 
                k -> MedicalDataTransformer.transformMedicalData(content));
        }
        
        /**
         * 清理缓存（可在定时任务中调用）
         */
        public static void clearCache() {
            transformCache.clear();
            log.info("医疗数据转换缓存已清理");
        }
        
        /**
         * 获取缓存统计信息
         */
        public static String getCacheStats() {
            return String.format("缓存大小: %d/%d", transformCache.size(), MAX_CACHE_SIZE);
        }
    }

    /**
     * 转换treatmentHistory数字编码为中文描述
     * @deprecated 已被 MedicalDataTransformer.transformMedicalData() 替代，建议使用新的统一转换方法
     * @param content JSON字符串
     * @return 转换后的JSON字符串
     */
    @Deprecated
    private String convertTreatmentHistory(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }

        // 处理多值数组情况，如[1,3] -> "腹透史,内瘘"
        Pattern pattern = Pattern.compile("\"treatmentHistory\":\\[(\\d+(?:,\\d+)*)\\]");
        Matcher matcher = pattern.matcher(content);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String[] values = matcher.group(1).split(",");
            StringBuilder result = new StringBuilder("\"treatmentHistory\":\"");
            for (int i = 0; i < values.length; i++) {
                if (i > 0) result.append(",");
                switch (values[i].trim()) {
                    case "1": result.append("腹透史"); break;
                    case "2": result.append("肾移植"); break;
                    case "3": result.append("内瘘"); break;
                    case "4": result.append("自体血管"); break;
                    case "5": result.append("人工血管"); break;
                    case "6": result.append("长期置管"); break;
                    case "7": result.append("临时置管"); break;
                    default: result.append("未知(").append(values[i]).append(")"); break;
                }
            }
            result.append("\"");
            matcher.appendReplacement(sb, result.toString());
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }

    /**
     * 转换病史相关字段的数字编码为中文描述
     * @param content JSON字符串
     * @return 转换后的JSON字符串
     */

    private String cleanMarkdownResult(String content) {
        return content.replace("```markdown", "").replace("---", "").replace("```", "");
    }

    private String convertMedicalHistoryFields(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        
        // 慢性病史转换 (chronicDiseasesHistory)
        content = content.replaceAll("\"chronicDiseasesHistory\":1", "\"chronicDiseasesHistory\":\"有\"");
        content = content.replaceAll("\"chronicDiseasesHistory\":2", "\"chronicDiseasesHistory\":\"无\"");
        
        // 疫苗接种史转换 (vaccination)
        content = content.replaceAll("\"vaccination\":1", "\"vaccination\":\"已接种\"");
        content = content.replaceAll("\"vaccination\":2", "\"vaccination\":\"未接种\"");
        
        // 食物过敏史转换 (foodAllergies)
        content = content.replaceAll("\"foodAllergies\":1", "\"foodAllergies\":\"有\"");
        content = content.replaceAll("\"foodAllergies\":2", "\"foodAllergies\":\"无\"");
        
        // 外伤手术史转换 (traumaSurgery)
        content = content.replaceAll("\"traumaSurgery\":1", "\"traumaSurgery\":\"有\"");
        content = content.replaceAll("\"traumaSurgery\":2", "\"traumaSurgery\":\"无\"");
        
        // 居住地转换 (placeResidence)
        content = content.replaceAll("\"placeResidence\":1", "\"placeResidence\":\"城市\"");
        content = content.replaceAll("\"placeResidence\":2", "\"placeResidence\":\"农村\"");
        
        // 不良习惯转换 (badHabit)
        content = content.replaceAll("\"badHabit\":1", "\"badHabit\":\"有\"");
        content = content.replaceAll("\"badHabit\":2", "\"badHabit\":\"无\"");
        
        // 接触史转换 (contact)
        content = content.replaceAll("\"contact\":1", "\"contact\":\"有\"");
        content = content.replaceAll("\"contact\":2", "\"contact\":\"无\"");
        
        // 家族史转换 (familyHistory) - 处理字符串数组格式 ["1","2"]
        Pattern familyPattern = Pattern.compile("\"familyHistory\":\\[\"(\\d+)\"(?:,\"(\\d+)\")*\\]");
        Matcher familyMatcher = familyPattern.matcher(content);
        StringBuffer familySb = new StringBuffer();
        
        while (familyMatcher.find()) {
            String fullMatch = familyMatcher.group(0);
            // 提取所有数字
            Pattern numPattern = Pattern.compile("\"(\\d+)\"");
            Matcher numMatcher = numPattern.matcher(fullMatch);
            StringBuilder result = new StringBuilder("\"familyHistory\":\"");
            boolean first = true;
            
            while (numMatcher.find()) {
                if (!first) result.append(",");
                first = false;
                switch (numMatcher.group(1)) {
                    case "1": result.append("高血压家族史"); break;
                    case "2": result.append("糖尿病家族史"); break;
                    case "3": result.append("心脏病家族史"); break;
                    case "4": result.append("肾病家族史"); break;
                    case "5": result.append("肿瘤家族史"); break;
                    default: result.append("其他家族史(").append(numMatcher.group(1)).append(")"); break;
                }
            }
            result.append("\"");
            familyMatcher.appendReplacement(familySb, result.toString());
        }
        familyMatcher.appendTail(familySb);
        
        return familySb.toString();
    }

    /**
     * 转换体格检查字段的数字编码为中文描述 - 最简单实现
     * @param content JSON字符串
     * @return 转换后的JSON字符串
     */
    private String convertPhysicalExamFields(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        
        try {

            // 0. 转换化验字段
            content = convertLabExamFields(content);

            // 1. 转换bodyInspect简单字段
            content = convertBodyInspectSimple(content);
            
            // 2. 转换generalSituation数组字段
            content = convertGeneralSituationArray(content);
            
            // 3. 转换skin数组字段
            content = convertSkinArray(content);
            
            // 4. 转换nervousSystem数组字段
            content = convertNervousSystemArray(content);


            
            log.info("体格检查字段转换完成");
            
        } catch (Exception e) {
            log.warn("体格检查字段转换失败: {}", e.getMessage());
        }

        return content;
    }

    /**
     * 转换bodyInspect简单字段
     */
    private String convertLabExamFields(String content) {
        // bodyInspect字段转换
        content = content.replaceAll("\"ecg\"", "\"胸部X线检查\"");
        content = content.replaceAll("\"chestXray\"", "\"胸部X线检查\"");
        content = content.replaceAll("\"otherLab\"", "\"其他化验检查\"");
        return content;
    }

    /**
     * 转换bodyInspect简单字段
     */
    private String convertBodyInspectSimple(String content) {
        // bodyInspect字段转换
        content = content.replaceAll("\"shape\":\"1\"", "\"shape\":\"正常\"");
        content = content.replaceAll("\"shape\":\"2\"", "\"shape\":\"肥胖\"");
        content = content.replaceAll("\"shape\":\"3\"", "\"shape\":\"消瘦\"");
        
        content = content.replaceAll("\"develop\":\"1\"", "\"develop\":\"正常\"");
        content = content.replaceAll("\"develop\":\"2\"", "\"develop\":\"不良\"");
        
        content = content.replaceAll("\"mind\":\"1\"", "\"mind\":\"清楚\"");
        content = content.replaceAll("\"mind\":\"2\"", "\"mind\":\"不清\"");
        
        content = content.replaceAll("\"position\":\"1\"", "\"position\":\"自主\"");
        content = content.replaceAll("\"position\":\"2\"", "\"position\":\"被动\"");


        
        return content;
    }

    /**
     * 转换generalSituation中的常见value值
     */
    private String convertGeneralSituationValues(String content) {
        // 体型：正常=1，肥胖=2，消瘦=3
        content = content.replaceAll("\"value\":\\[\"1\"\\]", "\"value\":\"正常\"");
        content = content.replaceAll("\"value\":\\[\"2\"\\]", "\"value\":\"肥胖\"");
        content = content.replaceAll("\"value\":\\[\"3\"\\]", "\"value\":\"消瘦\"");
        
        // 发育：正常=1，不良=2
        content = content.replaceAll("\"value\":\\[\"1\"\\]", "\"value\":\"正常\"");
        content = content.replaceAll("\"value\":\\[\"2\"\\]", "\"value\":\"不良\"");
        
        // 入科方式：平车=1，轮椅=2，步行=3
        content = content.replaceAll("\"value\":\\[\"3\"\\]", "\"value\":\"步行\"");
        content = content.replaceAll("\"value\":\\[\"1\"\\]", "\"value\":\"平车\"");
        content = content.replaceAll("\"value\":\\[\"2\"\\]", "\"value\":\"轮椅\"");
        
        // 神志：清楚=1，不清=2
        content = content.replaceAll("\"value\":\\[\"1\"\\]", "\"value\":\"清楚\"");
        content = content.replaceAll("\"value\":\\[\"2\"\\]", "\"value\":\"不清\"");
        
        // 体位：自主=1，被动=2
        content = content.replaceAll("\"value\":\\[\"1\"\\]", "\"value\":\"自主\"");
        content = content.replaceAll("\"value\":\\[\"2\"\\]", "\"value\":\"被动\"");
        
        // 步态：正常=1，蹒跚=2
        content = content.replaceAll("\"value\":\\[\"1\"\\]", "\"value\":\"正常\"");
        content = content.replaceAll("\"value\":\\[\"2\"\\]", "\"value\":\"蹒跚\"");
        
        // 查体：合作=1，不合作=2
        content = content.replaceAll("\"value\":\\[\"1\"\\]", "\"value\":\"合作\"");
        content = content.replaceAll("\"value\":\\[\"2\"\\]", "\"value\":\"不合作\"");
        
        // 语言：流利=1，迟钝=2，障碍=3
        content = content.replaceAll("\"value\":\\[\"1\"\\]", "\"value\":\"流利\"");
        content = content.replaceAll("\"value\":\\[\"2\"\\]", "\"value\":\"迟钝\"");
        content = content.replaceAll("\"value\":\\[\"3\"\\]", "\"value\":\"障碍\"");
        
        // 病容：急性=1，慢性=2
        content = content.replaceAll("\"value\":\\[\"1\"\\]", "\"value\":\"急性\"");
        content = content.replaceAll("\"value\":\\[\"2\"\\]", "\"value\":\"慢性\"");
        
        return content;
    }

    /**
     * 转换generalSituation数组字段
     */
    private String convertGeneralSituationArray(String content) {
        try {
            JsonNode root = objectMapper.readTree(content);
            JsonNode generalSituation = root.get("generalSituation");
            
            if (generalSituation != null && generalSituation.isArray()) {
                for (JsonNode item : generalSituation) {
                    JsonNode valueNode = item.get("value");
                    JsonNode listNode = item.get("list");
                    
                    if (valueNode != null && valueNode.isArray() && listNode != null && listNode.isArray()) {
                        // 根据list映射转换value
                        for (int i = 0; i < valueNode.size(); i++) {
                            String valueStr = valueNode.get(i).asText();
                            String label = findLabelInList(listNode, valueStr);
                            if (StringUtils.isNotBlank(label)) {
                                ((com.fasterxml.jackson.databind.node.ArrayNode) valueNode).set(i, objectMapper.valueToTree(label));
                            }
                        }
                        
                        // 如果只有一个值，转换为字符串
                        if (valueNode.size() == 1) {
                            ((com.fasterxml.jackson.databind.node.ObjectNode) item).set("value", valueNode.get(0));
                        }
                    }
                }
                
                return objectMapper.writeValueAsString(root);
            }
        } catch (Exception e) {
            log.warn("转换generalSituation失败: {}", e.getMessage());
        }
        
        return content;
    }

    /**
     * 转换skin数组字段
     */
    private String convertSkinArray(String content) {
        try {
            JsonNode root = objectMapper.readTree(content);
            JsonNode skin = root.get("skin");
            
            if (skin != null && skin.isArray()) {
                for (JsonNode item : skin) {
                    JsonNode valueNode = item.get("value");
                    JsonNode listNode = item.get("list");
                    
                    if (valueNode != null && valueNode.isArray() && listNode != null && listNode.isArray()) {
                        // 根据list映射转换value
                        for (int i = 0; i < valueNode.size(); i++) {
                            String valueStr = valueNode.get(i).asText();
                            String label = findLabelInList(listNode, valueStr);
                            if (StringUtils.isNotBlank(label)) {
                                ((com.fasterxml.jackson.databind.node.ArrayNode) valueNode).set(i, objectMapper.valueToTree(label));
                            }
                        }
                        
                        // 如果只有一个值，转换为字符串
                        if (valueNode.size() == 1) {
                            ((com.fasterxml.jackson.databind.node.ObjectNode) item).set("value", valueNode.get(0));
                        }
                    }
                }
                
                return objectMapper.writeValueAsString(root);
            }
        } catch (Exception e) {
            log.warn("转换skin失败: {}", e.getMessage());
        }
        
        return content;
    }

    /**
     * 转换nervousSystem数组字段
     */
    private String convertNervousSystemArray(String content) {
        try {
            JsonNode root = objectMapper.readTree(content);
            JsonNode nervousSystem = root.get("nervousSystem");
            
            if (nervousSystem != null && nervousSystem.isArray()) {
                for (JsonNode item : nervousSystem) {
                    JsonNode valueNode = item.get("value");
                    JsonNode listNode = item.get("list");
                    
                    if (valueNode != null && valueNode.isArray() && listNode != null && listNode.isArray()) {
                        // 根据list映射转换value
                        for (int i = 0; i < valueNode.size(); i++) {
                            String valueStr = valueNode.get(i).asText();
                            String label = findLabelInList(listNode, valueStr);
                            if (StringUtils.isNotBlank(label)) {
                                ((com.fasterxml.jackson.databind.node.ArrayNode) valueNode).set(i, objectMapper.valueToTree(label));
                            }
                        }
                        
                        // 如果只有一个值，转换为字符串；多个值保持数组
                        if (valueNode.size() == 1) {
                            ((com.fasterxml.jackson.databind.node.ObjectNode) item).set("value", valueNode.get(0));
                        }
                    }
                }
                
                return objectMapper.writeValueAsString(root);
                }
            } catch (Exception e) {
            log.warn("转换nervousSystem失败: {}", e.getMessage());
        }
        
        return content;
    }

    /**
     * 在list中查找value对应的label
     */
    private String findLabelInList(JsonNode listNode, String value) {
        for (JsonNode listItem : listNode) {
            JsonNode valueInList = listItem.get("value");
            JsonNode labelInList = listItem.get("label");
            
            if (valueInList != null && labelInList != null && 
                value.equals(valueInList.asText())) {
                return labelInList.asText();
            }
        }
        return "";
    }

    /**
     * 优化病历格式以提升AI评分准确性
     * @param content JSON字符串
     * @return 优化后的JSON字符串
     */
    private String optimizeMedicalRecordFormat(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        
        try {
            JsonNode root = objectMapper.readTree(content);
            
            // 1. 优化主诉格式
            content = optimizeChiefComplaint(content, root);
            
            // 2. 规范化诊断顺序
            content = standardizeDiagnosisOrder(content, root);
            
            log.info("病历格式优化完成");
            
        } catch (Exception e) {
            log.warn("病历格式优化失败: {}", e.getMessage());
        }
        
        return content;
    }

    /**
     * 优化主诉格式
     */
    private String optimizeChiefComplaint(String content, JsonNode root) {
        try {
            JsonNode mainSuitNode = root.get("mainSuit");
            JsonNode presentMedicalNode = root.get("presentMedical");
            
            if (mainSuitNode != null && presentMedicalNode != null) {
                String mainSuit = mainSuitNode.asText();
                String presentMedical = presentMedicalNode.asText();
                
                // 检查主诉是否缺乏症状描述
                if (mainSuit.contains("维持性血液透析") && !mainSuit.contains("乏力") && !mainSuit.contains("水肿")) {
                    // 从现病史中提取症状
                    String symptoms = extractSymptomsFromText(presentMedical);
                    if (StringUtils.isNotBlank(symptoms)) {
                        String enhancedMainSuit = symptoms + "，" + mainSuit;
                        content = content.replace("\"mainSuit\":\"" + mainSuit + "\"", 
                                                "\"mainSuit\":\"" + enhancedMainSuit + "\"");
                        log.info("主诉已优化: {} -> {}", mainSuit, enhancedMainSuit);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("主诉优化失败: {}", e.getMessage());
        }
        
        return content;
    }

    /**
     * 从文本中提取症状关键词
     */
    private String extractSymptomsFromText(String text) {
        if (StringUtils.isBlank(text)) {
            return "";
        }
        
        // 定义常见症状关键词
        String[] symptomKeywords = {
            "头晕", "乏力", "下肢浮肿", "恶心", "呕吐", "食欲减退",
            "胸闷", "心悸", "气促", "头痛", "水肿", "尿少", "无尿"
        };
        
        StringBuilder symptoms = new StringBuilder();
        for (String keyword : symptomKeywords) {
            if (text.contains(keyword)) {
                if (symptoms.length() > 0) {
                    symptoms.append("、");
                }
                symptoms.append(keyword);
                
                // 限制症状数量，避免主诉过长
                if (symptoms.toString().split("、").length >= 3) {
                    break;
                }
            }
        }
        
        return symptoms.toString();
    }

    /**
     * 规范化诊断顺序
     */
    private String standardizeDiagnosisOrder(String content, JsonNode root) {
        try {
            JsonNode diagnosisNode = root.get("tentativeDiagnosis");
            
            if (diagnosisNode != null) {
                String originalDiagnosis = diagnosisNode.asText();
                String reorderedDiagnosis = reorderDiagnosisByStandard(originalDiagnosis);
                
                if (!originalDiagnosis.equals(reorderedDiagnosis)) {
                    content = content.replace("\"tentativeDiagnosis\":\"" + originalDiagnosis + "\"",
                                            "\"tentativeDiagnosis\":\"" + reorderedDiagnosis + "\"");
                    log.info("诊断顺序已规范化: {} -> {}", originalDiagnosis, reorderedDiagnosis);
                }
            }
        } catch (Exception e) {
            log.warn("诊断顺序规范化失败: {}", e.getMessage());
        }
        
        return content;
    }

    /**
     * 按标准重新排序诊断
     */
    private String reorderDiagnosisByStandard(String diagnosis) {
        if (StringUtils.isBlank(diagnosis)) {
            return diagnosis;
        }
        
        String[] diagnosisArray = diagnosis.split(",");
        
        // 分类诊断
        List<String> treatmentDiagnosis = new ArrayList<>();  // 治疗诊断
        List<String> causeDiagnosis = new ArrayList<>();      // 病因诊断
        List<String> complicationDiagnosis = new ArrayList<>(); // 并发症诊断
        
        for (String item : diagnosisArray) {
            String trimmed = item.trim();
            
            // 治疗诊断
            if (trimmed.contains("维持性血透状态") || trimmed.contains("血液透析")) {
                treatmentDiagnosis.add(trimmed);
            }
            // 病因诊断
            else if (trimmed.contains("慢性肾脏病") || trimmed.contains("肾脏病")) {
                causeDiagnosis.add(trimmed);
            }
            // 并发症诊断
            else {
                complicationDiagnosis.add(trimmed);
            }
        }
        
        // 重新组合
        List<String> reorderedList = new ArrayList<>();
        reorderedList.addAll(treatmentDiagnosis);
        reorderedList.addAll(causeDiagnosis);
        reorderedList.addAll(complicationDiagnosis);
        
        return String.join(",", reorderedList);
    }

    private String getOriginalMedicalRecordFromOutpatientBlood(Long patientId) {
        OutpatientBloodRespVO outpatientBlood = outpatientBloodService.getOutpatientBlood(patientId);
        if (outpatientBlood == null) return null;
        return outpatientBlood.getPatientId()  > 0 ? outpatientBlood.toString() : null;
    }


    /**
     * 最小实现：仅从首次病程记录获取治疗信息，无降级
     */
    private String getTreatmentInfoFromFirstCourseRecord(Long patientId) {
        FirstCourseRecordCreateReqVO createReqVO = new FirstCourseRecordCreateReqVO();
        createReqVO.setPatientId(patientId);
        FirstCourseRecordRespVO firstCourseRecord = firstCourseRecordService.getFirstCourseRecord(createReqVO);
        if (firstCourseRecord == null) return null;
        return firstCourseRecord.getInitDiagnosis();
    }


    /**
     * 估算文本的token数量（粗略估算）
     */


    /**
     * 优化文本长度，避免超过模型限制
     */
    private String optimizeTextLength(String text, String textType) {
        if (text == null || text.trim().isEmpty()) {
        return text;
        }

        // 设置不同文本类型的最大长度限制
        int maxLength;
        switch (textType) {
            case "病历内容":
                maxLength = 30000; // 病历内容最大8000字符
                break;
            case "诊疗信息":
                maxLength = 30000; // 诊疗信息最大4000字符
                break;
            default:
                maxLength = 30000;
                break;
        }

        if (text.length() <= maxLength) {
            return text;
        }

        log.warn("{}长度超限，原始长度: {}, 限制长度: {}, 将进行截取", textType, text.length(), maxLength);

        // 智能截取：优先保留开头和结尾的重要信息
        int headLength = (int) (maxLength * 0.6); // 60%保留开头
        int tailLength = (int) (maxLength * 0.3); // 30%保留结尾

        String head = text.substring(0, Math.min(headLength, text.length()));
        String tail = "";

        if (text.length() > headLength + tailLength) {
            tail = text.substring(text.length() - tailLength);
        }

        String optimized = head + "\n\n[... 中间内容已省略 ...]\n\n" + tail;
        log.info("{}截取完成，优化后长度: {}", textType, optimized.length());

        return optimized;
    }

    private List<String> extractThinkTab(String text) {
        List<String> resultList = new ArrayList<>();
        if (text == null || text.trim().isEmpty()) {
            resultList.add("");
            resultList.add("");
            return resultList;
        }
        
        if (!text.contains("<think>") || !text.contains("</think>")) {
            // 没有think标签，全部内容作为第二项
            resultList.add(""); // 第一项为空
            resultList.add(text);
            return resultList;
        }
        
        int start = text.indexOf("<think>");
        int end = text.indexOf("</think>");
        
        if (start == -1 || end == -1 || end <= start) {
            // think标签不完整，全部内容作为第二项
            resultList.add("");
            resultList.add(text);
            return resultList;
        }
        
        // 提取think内容
        String thinkContent = text.substring(start + 7, end);
        
        // 安全提取其余内容
        String before = (start > 0) ? text.substring(0, start) : "";
        String after = (end + 8 < text.length()) ? text.substring(end + 8) : "";
        String otherContent = (before + after).trim();
        
        resultList.add(thinkContent);
        resultList.add(otherContent);
        return resultList;
    }

    // ==================== 重构后的私有方法 ====================
    
    /**
     * 验证请求参数
     */
    private void validateRequest(AiScoreCreateReqVO reqVO) {
        if (reqVO.getPatientId() == null) {
            throw new ServiceException(400, "患者ID不能为空");
        }
    }
    
    /**
     * 准备输入数据
     */
    private AiReportInputData prepareInputData(AiScoreCreateReqVO reqVO) {
        List<String> medicalRecordData = getMedicalRecordFromOutpatientBlood(reqVO.getPatientId());
        String medicalRecord = (medicalRecordData != null && !medicalRecordData.isEmpty()) ? medicalRecordData.get(0) : null;
        String patientName = (medicalRecordData != null && medicalRecordData.size() > 1) ? medicalRecordData.get(1) : "未知";
        String treatmentInfo = getTreatmentInfoFromFirstCourseRecord(reqVO.getPatientId());
        String patientInfoData = JSON.toJSONString(reqVO.getPatientInfo());
        String renalCheckout = this.getRenalCheck(reqVO.getPatientId());

        // 验证核心数据
        if (medicalRecord == null && treatmentInfo == null) {
            log.warn("患者 {} 的病历和诊疗信息均为空，无法生成任何报告。", reqVO.getPatientId());
            throw new ServiceException(400, "病历和诊疗信息为空，无法生成AI报告。");
        }

        // 优化文本长度
        String optimizedMedicalRecord = optimizeTextLength(medicalRecord, "病历内容");
        String optimizedTreatmentInfo = optimizeTextLength(treatmentInfo, "诊疗信息");

        return new AiReportInputData(
            reqVO.getPatientId(),
            patientName,
            medicalRecord,
            treatmentInfo,
            optimizedMedicalRecord,
            optimizedTreatmentInfo,
            patientInfoData,
            renalCheckout
        );
    }
    
    /**
     * 生成AI报告（支持部分并行处理）
     */
    private AiReportResults generateAiReports(AiReportInputData inputData) {
        try {
            // 1. 先生成评分报告（其他报告依赖此结果）
            String scorePrompt = aiService.buildPrompt("score", 
                inputData.getOptimizedMedicalRecord(), 
                inputData.getOptimizedTreatmentInfo(), 
                inputData.getPatientInfoData());
            
            List<String> scoreResponses = aiService.scoreMedicalRecord(scorePrompt);
            String scoreResultJson = null;
            String aiReasoning = null;
            if (scoreResponses != null && !scoreResponses.isEmpty()) {
                scoreResultJson = scoreResponses.get(0);
                if (scoreResponses.size() > 1) {
                    aiReasoning = scoreResponses.get(1);
                }
            }
            log.info("评分报告生成完成。");

            // 2. 并行生成查检和风险报告
            CompletableFuture<List<String>> checkReportFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    String checkPrompt = aiService.buildPrompt("check", 
                        inputData.getMedicalRecord(), 
                        inputData.getTreatmentInfo(), 
                        inputData.getPatientInfoData());
                    String checkResultMarkdown = aiService.getAiRawResponse(checkPrompt);
                    log.info("查检报告生成完成。");
                    return extractThinkTab(checkResultMarkdown);
                } catch (Exception e) {
                    log.error("生成查检报告失败，患者ID: {}", inputData.getPatientId(), e);
                    List<String> fallback = new ArrayList<>();
                    fallback.add("ERROR"); // 标记为错误状态
                    fallback.add("查检报告生成失败：" + e.getMessage());
                    return fallback;
                }
            });

            CompletableFuture<List<String>> riskReportFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    String riskPrompt = aiService.buildPrompt("risk", 
                        inputData.getMedicalRecord(), 
                        inputData.getTreatmentInfo(), 
                        inputData.getRenalCheckout(), 
                        inputData.getPatientInfoData());
                    String riskResultMarkdown = aiService.getAiRawResponse(riskPrompt);
                    log.info("风险报告生成完成。");
                    return extractThinkTab(riskResultMarkdown);
                } catch (Exception e) {
                    log.error("生成风险报告失败，患者ID: {}", inputData.getPatientId(), e);
                    List<String> fallback = new ArrayList<>();
                    fallback.add("ERROR"); // 标记为错误状态
                    fallback.add("风险报告生成失败：" + e.getMessage());
                    return fallback;
                }
            });

            // 等待并行任务完成（带超时处理）
            List<String> checkResults;
            List<String> riskResults;
            
            try {
                checkResults = checkReportFuture.get(3, TimeUnit.MINUTES);
                riskResults = riskReportFuture.get(3, TimeUnit.MINUTES);
            } catch (TimeoutException e) {
                log.warn("AI报告生成超时，降级到串行处理");
                // 降级到串行处理
                checkReportFuture.cancel(true);
                riskReportFuture.cancel(true);
                
                checkResults = generateCheckReportFallback(inputData);
                riskResults = generateRiskReportFallback(inputData);
            }

            return new AiReportResults(scoreResultJson, aiReasoning, checkResults, riskResults, scorePrompt);
            
        } catch (Exception e) {
            log.error("生成AI报告过程中发生异常", e);
            throw new ServiceException(500, "AI报告生成失败：" + e.getMessage());
        }
    }
    
    /**
     * 查检报告降级处理
     */
    private List<String> generateCheckReportFallback(AiReportInputData inputData) {
        try {
            String checkPrompt = aiService.buildPrompt("check", 
                inputData.getMedicalRecord(), 
                inputData.getTreatmentInfo(), 
                inputData.getPatientInfoData());
            String checkResultMarkdown = aiService.getAiRawResponse(checkPrompt);
            log.info("查检报告(降级)生成完成。");
            return extractThinkTab(checkResultMarkdown);
        } catch (Exception e) {
            log.error("查检报告降级处理也失败，患者ID: {}", inputData.getPatientId(), e);
            List<String> fallback = new ArrayList<>();
            fallback.add("ERROR"); // 标记为错误状态
            fallback.add("查检报告生成失败：" + e.getMessage());
            return fallback;
        }
    }
    
    /**
     * 风险报告降级处理
     */
    private List<String> generateRiskReportFallback(AiReportInputData inputData) {
        try {
            String riskPrompt = aiService.buildPrompt("risk", 
                inputData.getMedicalRecord(), 
                inputData.getTreatmentInfo(), 
                inputData.getRenalCheckout(), 
                inputData.getPatientInfoData());
            String riskResultMarkdown = aiService.getAiRawResponse(riskPrompt);
            log.info("风险报告(降级)生成完成。");
            return extractThinkTab(riskResultMarkdown);
        } catch (Exception e) {
            log.error("风险报告降级处理也失败，患者ID: {}", inputData.getPatientId(), e);
            List<String> fallback = new ArrayList<>();
            fallback.add("ERROR"); // 标记为错误状态
            fallback.add("风险报告生成失败：" + e.getMessage());
            return fallback;
        }
    }
    
    /**
     * 保存报告结果到数据库
     */
    private AiScoreDO saveReportResults(AiScoreCreateReqVO reqVO, AiReportInputData inputData, AiReportResults reportResults) {
        AiScoreDO aiScoreDO = new AiScoreDO();
        aiScoreDO.setPatientId(reqVO.getPatientId());
        aiScoreDO.setPatient_name(inputData.getPatientName());
        aiScoreDO.setPatientBasicInfo(inputData.getMedicalRecord());
        aiScoreDO.setTreatmentInfo(inputData.getTreatmentInfo());
        aiScoreDO.setAiModel(aiConfig.getModel());
        aiScoreDO.setAiPrompt(reportResults.getScorePrompt());

        // 填充各报告结果
        aiScoreDO.setScoreResult(reportResults.getScoreResultJson());
        aiScoreDO.setContent(reportResults.getScoreResultJson());
        
        // 安全获取查检报告结果
        String checkResult = (reportResults.getCheckResults() != null && reportResults.getCheckResults().size() > 1) 
            ? cleanMarkdownResult(reportResults.getCheckResults().get(1)) 
            : "查检报告生成失败";
        aiScoreDO.setCheckResult(checkResult);
        
        // 安全获取风险报告结果
        String riskResult = (reportResults.getRiskResults() != null && reportResults.getRiskResults().size() > 1) 
            ? cleanMarkdownResult(reportResults.getRiskResults().get(1)) 
            : "风险报告生成失败";
        aiScoreDO.setRiskResult(riskResult);
        
        aiScoreDO.setAiReasoning(reportResults.getAiReasoning());

        // 解析评分JSON并处理异常
        parseAndSetScoreData(aiScoreDO, reportResults.getScoreResultJson());

        // 保存到数据库
        try {
            aiScoreMapper.insert(aiScoreDO);
            log.info("AI系列报告保存成功, 记录ID: {}", aiScoreDO.getId());
            return aiScoreDO;
        } catch (Exception e) {
            log.error("保存AI系列报告到数据库失败, 患者ID: {}", reqVO.getPatientId(), e);
            throw new ServiceException(500, "数据库保存失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析并设置评分数据
     */
    private void parseAndSetScoreData(AiScoreDO aiScoreDO, String scoreResultJson) {
        if (StringUtils.isNotBlank(scoreResultJson)) {
            try {
                JsonNode scoreNode = objectMapper.readTree(scoreResultJson);
                aiScoreDO.setScoreTotal(scoreNode.path("totalScore").asInt(0));
                aiScoreDO.setScoreLevel(scoreNode.path("level").asText("未评级"));
            } catch (Exception e) {
                log.error("解析评分JSON以提取总分和等级时出错: {}", e.getMessage());
                // JSON解析异常返回纯文本
                aiScoreDO.setScoreTotal(0);
                aiScoreDO.setScoreLevel("JSON解析失败，原始内容：" + scoreResultJson);
            }
        }
    }
    
    // ==================== 数据传输对象 ====================
    
    /**
     * AI报告输入数据
     */
    private static class AiReportInputData {
        private final Long patientId;
        private final String patientName;
        private final String medicalRecord;
        private final String treatmentInfo;
        private final String optimizedMedicalRecord;
        private final String optimizedTreatmentInfo;
        private final String patientInfoData;
        private final String renalCheckout;
        
        public AiReportInputData(Long patientId, String patientName, String medicalRecord, 
                                String treatmentInfo, String optimizedMedicalRecord, 
                                String optimizedTreatmentInfo, String patientInfoData, String renalCheckout) {
            this.patientId = patientId;
            this.patientName = patientName;
            this.medicalRecord = medicalRecord;
            this.treatmentInfo = treatmentInfo;
            this.optimizedMedicalRecord = optimizedMedicalRecord;
            this.optimizedTreatmentInfo = optimizedTreatmentInfo;
            this.patientInfoData = patientInfoData;
            this.renalCheckout = renalCheckout;
        }
        
        // Getters
        public Long getPatientId() { return patientId; }
        public String getPatientName() { return patientName; }
        public String getMedicalRecord() { return medicalRecord; }
        public String getTreatmentInfo() { return treatmentInfo; }
        public String getOptimizedMedicalRecord() { return optimizedMedicalRecord; }
        public String getOptimizedTreatmentInfo() { return optimizedTreatmentInfo; }
        public String getPatientInfoData() { return patientInfoData; }
        public String getRenalCheckout() { return renalCheckout; }
    }
    
    /**
     * AI报告结果
     */
    private static class AiReportResults {
        private final String scoreResultJson;
        private final String aiReasoning;
        private final List<String> checkResults;
        private final List<String> riskResults;
        private final String scorePrompt;
        
        public AiReportResults(String scoreResultJson, String aiReasoning, 
                              List<String> checkResults, List<String> riskResults, String scorePrompt) {
            this.scoreResultJson = scoreResultJson;
            this.aiReasoning = aiReasoning;
            this.checkResults = checkResults;
            this.riskResults = riskResults;
            this.scorePrompt = scorePrompt;
        }
        
        // Getters
        public String getScoreResultJson() { return scoreResultJson; }
        public String getAiReasoning() { return aiReasoning; }
        public List<String> getCheckResults() { return checkResults; }
        public List<String> getRiskResults() { return riskResults; }
        public String getScorePrompt() { return scorePrompt; }
    }

}
