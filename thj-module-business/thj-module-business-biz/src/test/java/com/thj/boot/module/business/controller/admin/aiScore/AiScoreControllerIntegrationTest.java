package com.thj.boot.module.business.controller.admin.aiScore;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.thj.boot.common.pojo.CommonResult;
import com.thj.boot.module.business.dal.datado.aiScore.AiScoreDO;
import com.thj.boot.module.business.dal.mapper.aiScore.AiScoreMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AiScoreController 集成测试
 * 测试完整的 Controller → Service → 数据库链路
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@Transactional // 确保测试数据回滚
class AiScoreControllerIntegrationTest {

    @LocalServerPort
    private int port;

    @Resource
    private AiScoreMapper aiScoreMapper;

    private RestTemplate restTemplate;
    private String baseUrl;

    @BeforeEach
    void setUp() {
        restTemplate = new RestTemplateBuilder().build();
        baseUrl = "http://localhost:" + port + "/business/ai-score";
        
        // 清理测试数据
        cleanTestData();
    }

    @Test
    void testGetAiScore_WithExistingData() {
        // 准备测试数据 - 在数据库中插入AI评分记录
        Long patientId = 1001L;
        AiScoreDO testData = createTestAiScoreData(patientId);
        aiScoreMapper.insert(testData);

        // 执行HTTP请求测试
        String url = baseUrl + "/get?patientId=" + patientId;
        @SuppressWarnings("rawtypes")
        CommonResult response = restTemplate.getForObject(url, CommonResult.class);

        // 验证HTTP响应
        assertNotNull(response, "响应不应为null");
        assertEquals(0, response.getCode(), "响应码应为0");
        assertNotNull(response.getData(), "响应数据不应为null");
        
        // 验证响应数据结构（基本验证，因为RestTemplate返回的是Map）
        assertTrue(response.getData() instanceof java.util.LinkedHashMap, "响应数据应为Map结构");
        @SuppressWarnings("unchecked")
        java.util.Map<String, Object> data = (java.util.Map<String, Object>) response.getData();
        assertEquals(85, data.get("totalScore"), "总分应为85");
        assertEquals("良好", data.get("level"), "等级应为良好");
    }

    @Test
    void testGetAiScore_WithNonExistentPatient() {
        // 使用不存在的患者ID
        Long nonExistentPatientId = 9999L;
        
        // 执行HTTP请求测试
        String url = baseUrl + "/get?patientId=" + nonExistentPatientId;
        @SuppressWarnings("rawtypes")
        CommonResult response = restTemplate.getForObject(url, CommonResult.class);

        // 验证HTTP响应
        assertNotNull(response, "响应不应为null");
        assertEquals(0, response.getCode(), "响应码应为0");
        
        // 对于不存在的患者，Service返回空的AiScoreRespVO对象
        assertNotNull(response.getData(), "响应数据不应为null（应返回空对象）");
        @SuppressWarnings("unchecked")
        java.util.Map<String, Object> data = (java.util.Map<String, Object>) response.getData();
        
        // 验证空对象的字段都是null或默认值
        assertNull(data.get("totalScore"), "不存在患者的总分应为null");
        assertNull(data.get("level"), "不存在患者的等级应为null");
    }

    @Test
    void testGetAiScore_WithValidPatientId() {
        // 测试正常的患者ID参数处理
        Long patientId = 1002L;
        
        // 执行HTTP请求测试（即使数据库中没有数据，也应正常返回）
        String url = baseUrl + "/get?patientId=" + patientId;
        @SuppressWarnings("rawtypes")
        CommonResult response = restTemplate.getForObject(url, CommonResult.class);

        // 验证HTTP响应基本结构
        assertNotNull(response, "响应不应为null");
        assertEquals(0, response.getCode(), "响应码应为0");
        assertNotNull(response.getData(), "响应数据不应为null");
        
        // 验证Controller -> Service链路正常工作
        assertTrue(response.getData() instanceof java.util.LinkedHashMap, "响应数据应为Map结构");
    }

    /**
     * 创建测试用的AI评分数据
     */
    private AiScoreDO createTestAiScoreData(Long patientId) {
        AiScoreDO aiScoreDO = new AiScoreDO();
        aiScoreDO.setPatientId(patientId);
        aiScoreDO.setPatient_name("测试患者");
        aiScoreDO.setScoreTotal(85);
        aiScoreDO.setScoreLevel("良好");
        aiScoreDO.setContent("测试AI评分内容");
        aiScoreDO.setExpertComment("测试专家点评");
        
        // 设置详细评分JSON
        String scoreResult = "{" +
                "\"totalScore\": 85," +
                "\"level\": \"良好\"," +
                "\"details\": {" +
                    "\"mainComplaint\": {\"maxScore\": 10, \"actualScore\": 8, \"comment\": \"主诉记录完整\"}," +
                    "\"medicalHistory\": {\"maxScore\": 20, \"actualScore\": 17, \"comment\": \"病史记录详细\"}," +
                    "\"physicalExam\": {\"maxScore\": 15, \"actualScore\": 12, \"comment\": \"体格检查规范\"}," +
                    "\"auxiliaryExam\": {\"maxScore\": 5, \"actualScore\": 4, \"comment\": \"辅助检查完善\"}," +
                    "\"diagnosis\": {\"maxScore\": 15, \"actualScore\": 13, \"comment\": \"诊断准确\"}," +
                    "\"treatment\": {\"maxScore\": 20, \"actualScore\": 18, \"comment\": \"治疗方案合理\"}," +
                    "\"overallEvaluation\": {\"maxScore\": 5, \"actualScore\": 4, \"comment\": \"整体评价良好\"}," +
                    "\"other\": {\"maxScore\": 10, \"actualScore\": 9, \"comment\": \"其他方面表现良好\"}" +
                "}" +
            "}";
        aiScoreDO.setScoreResult(scoreResult);
        
        // 设置时间字段
        Date now = new Date();
        aiScoreDO.setCreateTime(now);
        aiScoreDO.setUpdateTime(now);
        aiScoreDO.setCreator("test");
        aiScoreDO.setUpdater("test");
        aiScoreDO.setDeleted(false);
        
        return aiScoreDO;
    }

    /**
     * 清理测试数据
     */
    private void cleanTestData() {
        // 删除可能存在的测试数据
        LambdaQueryWrapper<AiScoreDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AiScoreDO::getPatientId, 1001L, 1002L, 9999L);
        aiScoreMapper.delete(wrapper);
    }
}