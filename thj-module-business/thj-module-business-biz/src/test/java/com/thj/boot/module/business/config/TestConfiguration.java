package com.thj.boot.module.business.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * 测试配置类
 * 用于Spring Boot测试时的配置
 *
 * <AUTHOR>
 */
@SpringBootConfiguration
@EnableAutoConfiguration(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = "com.thj.boot.module.business")
@MapperScan("com.thj.boot.module.business.dal.mapper")
public class TestConfiguration {
}