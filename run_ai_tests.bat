@echo off
chcp 65001 >nul
echo ========================================
echo           AI编码修复测试套件
echo ========================================
echo.

echo [INFO] 正在运行AI编码修复集成测试...
echo.

REM 运行AI编码修复测试
call mvn test -Dtest="AiEncodingFixTest" -DskipTests=false -pl thj-module-business/thj-module-business-biz
set TEST_RESULT=%ERRORLEVEL%

echo.
echo ========================================
echo                测试结果
echo ========================================

if %TEST_RESULT% EQU 0 (
    echo [SUCCESS] AI编码修复测试全部通过！
    echo [SUCCESS] 乱码问题修复验证成功
    echo [SUCCESS] AI服务功能正常
    exit /b 0
) else (
    echo [FAILED] AI编码修复测试失败
    echo [INFO] 请检查以下内容：
    echo   - prompt模板文件是否存在
    echo   - 编码设置是否正确
    echo   - Spring Boot配置是否正确
    exit /b 1
)
